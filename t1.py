# -*- coding: utf-8 -*-
"""
نموذج "أمؤلي-T1" للتعلم العميق الذاتي من الإنترنت وحفظه في ملف.
تعريفات:
- يجمع نصوص مقالات ويكيبيديا العربية لمجموعة مواضيع.
- ينظف النصوص ويولد تسلسلات لتدريب شبكة LSTM أعمق.
- يحفظ ويحمّل الموديل والـTokenizer.
- يدرب نفسه ذاتيًا كل 24 ساعة، ثم يتيح التفاعل واستكمال الجمل.
"""

import os
import time
import requests
import re
import pickle
import numpy as np
from bs4 import BeautifulSoup
import datetime

# مكتبات TensorFlow/Keras للتعلم العميق
import tensorflow as tf

# تفعيل التنفيذ التفاعلي لتفادي أخطاء numpy() في Graph Mode
tf.config.run_functions_eagerly(True)
try:
    tf.compat.v1.enable_eager_execution()
except:
    pass

from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Embedding, LSTM, Dense
from tensorflow.keras.utils import to_categorical

# اسم الموديل وملفات الحفظ
MODEL_NAME = "ml-T1"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
ARTICLES_DUMP = "wikipedia_dump.txt"
MAX_SEQUENCE_LEN = 8   # نستخدم 7 كلمات للتنبؤ بالكلمة الثامنة

#############################
# 1. التحقق من اتصال الإنترنت
#############################

def is_connected() -> bool:
    """
    يتحقق من وجود اتصال بالإنترنت عبر الوصول إلى google.com.
    """
    try:
        requests.get("https://www.google.com", timeout=5)
        return True
    except:
        return False

#############################
# 2. جمع المقالات من ويكيبيديا
#############################

def fetch_wikipedia_article(title: str) -> str:
    """
    يحمل نص المقالة من ويكيبيديا العربية بعنوان 'title'.
    يعيد نص الفقرات (<p>) كنص موحد.
    """
    url = f"https://ar.wikipedia.org/wiki/{title}"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"⚠️ فشل تحميل الصفحة: {title} (الحالة: {response.status_code})")
            return ""
        soup = BeautifulSoup(response.text, 'html.parser')
        paragraphs = soup.find_all('p')
        text = ""
        for p in paragraphs:
            text += p.get_text()
        return text
    except Exception as e:
        print(f"⚠️ خطأ في تحميل {title}: {e}")
        return ""

def collect_articles(topics: list, delay: float = 0.1) -> str:
    """
    يجمع نصوص جميع المقالات في قائمة 'topics' بشكل متسلسل مع تأخير بسيط.
    يحفظ النصوص في متغير ويعيده.
    """
    all_text = []
    for idx, topic in enumerate(topics):
        print(f"تحميل ({idx+1}/{len(topics)}) موضوع: {topic} ...")
        art = fetch_wikipedia_article(topic)
        if art:
            all_text.append(art)
        time.sleep(delay)
    combined = "\n".join(all_text)
    # حفظ نسخة من النصوص في ملف
    with open(ARTICLES_DUMP, "w", encoding="utf-8") as f:
        f.write(combined)
    print(f"📝 تم حفظ المقالات في: {ARTICLES_DUMP}")
    return combined

#############################
# 3. تجهيز النصوص للتدريب
#############################

def preprocess_text(text: str) -> str:
    """
    ينظف النص: يحذف كل ما هو ليس حرفًا عربيًا أو مسافة.
    ثم يقلص المسافات المتكررة ويعيد النص نظيفًا.
    """
    text = re.sub(r'[^\u0600-\u06FF\s]', ' ', text)   # إزالة أي رمز ليس عربيًا
    text = re.sub(r'\s+', ' ', text).strip()          # إزالة المسافات المتكررة
    return text

def create_sequences(tokenizer: Tokenizer, corpus: list, max_sequence_len: int):
    """
    يحمّل النصوص المُعالجة عبر الـ tokenizer ثم يولّد تسلسلات N-gram:
    لكل جملة، ينشئ n-gram sequences حيث n = max_sequence_len.
    يعيد X (تسلسلات الإدخال) و y (الكلمة الهدف) بعد التصنيف إلى one-hot.
    """
    input_sequences = []
    for line in corpus:
        token_list = tokenizer.texts_to_sequences([line])[0]
        for i in range(max_sequence_len, len(token_list) + 1):
            n_gram_sequence = token_list[i - max_sequence_len: i]
            input_sequences.append(n_gram_sequence)
    input_sequences = np.array(input_sequences)
    
    # تقسيم التسلسلات إلى X و y
    X = input_sequences[:, :-1]  # كل التسلسل عدا الكلمة الأخيرة هو المدخل
    y = input_sequences[:, -1]   # الكلمة الأخيرة هي الهدف
    
    y = to_categorical(y, num_classes=len(tokenizer.word_index) + 1)
    return X, y

#############################
# 4. بناء الشبكة العميقة
#############################

def build_model(vocab_size: int, max_sequence_len: int):
    """
    يبني نموذج Keras أكبر: Embedding → LSTM(256) → Dense(softmax).
    """
    model = Sequential()
    model.add(Embedding(input_dim=vocab_size, output_dim=128, input_length=max_sequence_len - 1))
    model.add(LSTM(256, return_sequences=False))
    model.add(Dense(vocab_size, activation='softmax'))
    model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])
    return model

#############################
# 5. حفظ وتحميل النموذج والـTokenizer
#############################

def save_tokenizer(tokenizer: Tokenizer, filename: str):
    with open(filename, 'wb') as f:
        pickle.dump(tokenizer, f)
    print(f"✅ تم حفظ الـ Tokenizer في: {filename}")

def load_tokenizer(filename: str) -> Tokenizer:
    if os.path.exists(filename):
        with open(filename, 'rb') as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل الـ Tokenizer من: {filename}")
        return tokenizer
    else:
        print("⚠️ ملف الـ Tokenizer غير موجود. سيتم إنشاء Tokenizer جديد.")
        return None

def save_model_file(model: tf.keras.Model, filename: str):
    model.save(filename)
    print(f"✅ تم حفظ الموديل في: {filename}")

def load_model_file(filename: str) -> tf.keras.Model:
    if os.path.exists(filename):
        model = load_model(filename)
        print(f"✅ تم تحميل الموديل من: {filename}")
        return model
    else:
        print("⚠️ لم يتم العثور على ملف الموديل. سيتم تدريب موديل جديد.")
        return None

#############################
# 6. تنظيف مدخل المستخدم
#############################

def clean_input(text: str) -> str:
    """
    ينظف مدخل المستخدم: يحذف أي رمز ليس عربيًا أو مسافة.
    """
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

#############################
# 7. دالة لتوليد نص (تنبؤ الكلمات تتابعي)
#############################

def generate_text(seed_text: str, next_words: int, model: tf.keras.Model,
                  tokenizer: Tokenizer, max_sequence_len: int) -> str:
    """
    يولّد نصًا بطول 'next_words' ابتداءً من 'seed_text' باستخدام الموديل المدرب.
    """
    output_text = seed_text
    for _ in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        predicted_probs = model.predict(token_list, verbose=0)[0]
        predicted_index = np.argmax(predicted_probs)
        predicted_word = tokenizer.index_word.get(predicted_index, "")
        if not predicted_word:
            break
        output_text += " " + predicted_word
    return output_text

#############################
# 8. التنفيذ الرئيسي مع حلقة التعلم الذاتي
#############################

if __name__ == "__main__":
    # ----------------------------------------------------
    # 8.1. قائمة المواضيع (topics) لجمع المعلومات منها
    # ----------------------------------------------------
    topics = [
        "الذكاء_الاصطناعي",
        "تعلم_الآلة",
        "الإنترنت",
        "البرمجيات",
        "أنظمة_التشغيل",
        "قواعد_البيانات",
        "الشبكات_الحاسوبية",
        "الأمن_السيبراني",
        "الطاقة_المتجددة",
        "الهندسة_الكهربائية",
        "الهندسة_الميكانيكية",
        "الهندسة_المدنية",
        "الفضاء",
        "علم_البيئة",
        "علوم_الحاسوب_النظري",
        "أتمتة_العمليات",
        "الحوسبة_السحابية",
        "الواقع_الافتراضي",
        "الواقع_المعزز",
        "العملات_الرقمية",
        "البلوكشين",
        "التجارة_الإلكترونية",
        "التعليم_عن_بعد",
        "الروبوتات_الصناعية",
        "الأمن_الوطني",
        "البيانات_الضخمة",
        "الهندسة_البيئية",
        "الذكاء_الاصطناعي_في_الطب",
        "أمن_الشبكات",
        "الحوسبة_الموزعة",
        "الشبكات_اللاسكيلية"
    ]

    # حلقة لا نهائية للتعلم الذاتي كل 24 ساعة
    while True:
        print(f"\n🕓 [{datetime.datetime.now()}] بدء دورة التعلم الذاتي للموديل '{MODEL_NAME}'")
        
        if is_connected():
            print("✅ تم اكتشاف اتصال بالإنترنت. سيتم تحديث البيانات والتدريب.\n")

            # 1) جمع المقالات من ويكيبيديا وحفظها
            raw_text = collect_articles(topics, delay=0.1)

            # 2) دمج نص محلي إن وجد (ملف extra_text.txt)
            if os.path.exists("extra_text.txt"):
                with open("extra_text.txt", "r", encoding="utf-8") as f:
                    raw_text += "\n" + f.read()
                print("📝 تم دمج محتوى extra_text.txt مع البيانات.")

            # 3) تنظيف النصوص وتجهيز قائمة الجمل
            cleaned_text = preprocess_text(raw_text)
            sentences = re.split(r'[\.؟!\n]', cleaned_text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 0]

            # 4) إعداد Tokenizer (تحميله أو إنشاء جديد)
            tokenizer = load_tokenizer(TOKENIZER_FILE)
            if tokenizer is None:
                tokenizer = Tokenizer()
                tokenizer.fit_on_texts(sentences)
                save_tokenizer(tokenizer, TOKENIZER_FILE)

            total_words = len(tokenizer.word_index) + 1
            print(f"🔤 حجم القاموس الحالي: {total_words} كلمة")
            
            # 5) إنشاء بيانات التدريب (X, y)
            X, y = create_sequences(tokenizer, sentences, MAX_SEQUENCE_LEN)
            print(f"📊 عدد عينات التدريب لهذه الدورة: {X.shape[0]}")

            # 6) تحميل الموديل أو بناء جديد
            model = load_model_file(MODEL_FILE)
            if model is None:
                print("📐 بناء موديل جديد ...")
                model = build_model(total_words, MAX_SEQUENCE_LEN)
            else:
                print("♻️ تم تحميل الموديل الموجود سابقًا. سيتم التدريب التراكمي (Fine-Tuning).")

            # 7) تدريب الموديل لعدة عصور (مثل 3) لإعطاء تحسن حقيقي
            print("🚀 بدء التدريب ...")
            model.fit(X, y, epochs=3, batch_size=128, verbose=1)

            # 8) حفظ الموديل بعد انتهاء هذه الدورة
            save_model_file(model, MODEL_FILE)
            print(f"✅ أنهت دورة التعلم الذاتي بنجاح في {datetime.datetime.now()}")

            # 9) تفعيل الوضع التفاعلي بعد الدورة (اختياري)
            print("\n🤖 موديل أمؤلي-T1 جاهز للإجابة حتى الدورة التالية!")
            print("✏️ اكتب جملة بالعربية للحصول على تكملة (أو 'تخطي' لتخطي التفاعل):")
            while True:
                seed = input("\n> ")
                seed = clean_input(seed)
                if seed.lower() in ["تخطي", "skip"]:
                    break
                if seed.lower() in ["خروج", "exit", "quit"]:
                    print("✨ تم إنهاء البرنامج. إلى اللقاء!")
                    exit()
                if not seed:
                    print("❌ من فضلك أدخل نصًا عربيًا صالحًا.")
                    continue

                completion = generate_text(seed, next_words=10, model=model,
                                           tokenizer=tokenizer, max_sequence_len=MAX_SEQUENCE_LEN)
                print(f"\n🔍 أمؤلي-T1 يكمل الجملة:\n{completion}")

        else:
            print("❌ لا يوجد اتصال بالإنترنت حاليًا. سيتم إعادة المحاولة في الدورة القادمة.")

        # 10) انتظر 24 ساعة (86400 ثانية) قبل بدء الدورة التالية
        print("\n⏳ الانتظار لمدة 24 ساعة قبل بدء الدورة التالية...\n")
        time.sleep(60 * 60 * 24)