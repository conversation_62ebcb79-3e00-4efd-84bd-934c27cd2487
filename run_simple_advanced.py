# -*- coding: utf-8 -*-
"""
تشغيل النموذج المتقدم المبسط للتفاعل المباشر
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model
import re

# إعدادات النموذج
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
MAX_SEQUENCE_LEN = 16

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

def generate_text_advanced(seed_text: str, next_words: int, model, tokenizer, max_sequence_len: int):
    """توليد نص متقدم"""
    output_text = seed_text
    
    for _ in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]
        
        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]
            
        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')
        
        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]
            
            # Top-K sampling
            top_k = 5
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]
            top_k_probs = predicted_probs[top_k_indices]
            top_k_probs = top_k_probs / np.sum(top_k_probs)
            predicted_index = np.random.choice(top_k_indices, p=top_k_probs)
            
            predicted_word = tokenizer.index_word.get(predicted_index, "")
            if not predicted_word:
                break
                
            output_text += " " + predicted_word
        except:
            break
    
    return output_text

def analyze_intent(text: str) -> str:
    """تحليل بسيط لنية المستخدم"""
    text_lower = text.lower()
    
    if any(word in text_lower for word in ["ما هو", "ما هي", "تعريف"]):
        return "تعريف"
    elif any(word in text_lower for word in ["كيف", "طريقة"]):
        return "طريقة"
    elif any(word in text_lower for word in ["لماذا", "سبب"]):
        return "سبب"
    elif any(word in text_lower for word in ["متى", "وقت"]):
        return "وقت"
    elif "؟" in text:
        return "سؤال"
    else:
        return "عام"

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً بك في أمؤلي-T1 المتقدم المبسط!")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    if not os.path.exists(MODEL_FILE):
        print(f"❌ ملف النموذج غير موجود: {MODEL_FILE}")
        print("يرجى تشغيل t1_simple_advanced.py أولاً")
        return
    
    if not os.path.exists(TOKENIZER_FILE):
        print(f"❌ ملف Tokenizer غير موجود: {TOKENIZER_FILE}")
        print("يرجى تشغيل t1_simple_advanced.py أولاً")
        return
    
    # تحميل النموذج
    print("📥 تحميل النموذج...")
    try:
        model = load_model(MODEL_FILE)
        print(f"✅ تم تحميل النموذج: {MODEL_FILE}")
        
        with open(TOKENIZER_FILE, "rb") as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل Tokenizer: {TOKENIZER_FILE}")
        
        print(f"🧠 النموذج جاهز مع {model.count_params():,} معامل")
        print(f"📚 حجم القاموس: {len(tokenizer.word_index):,} كلمة")
        
    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return
    
    # حلقة التفاعل
    print("\n🎯 النموذج جاهز للتفاعل!")
    print("💡 يمكنك:")
    print("   - طرح أسئلة: 'ما هو الذكاء الاصطناعي؟'")
    print("   - كتابة جملة للتكملة: 'في المستقبل ستكون التقنية'")
    print("   - كتابة 'خروج' لإنهاء البرنامج")
    print("-" * 50)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input(f"\n[{conversation_count + 1}] 🔤 أدخل نصك: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ["خروج", "exit", "quit"]:
                print("👋 شكراً لاستخدام أمؤلي-T1!")
                break
            
            # تنظيف الإدخال
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صالح")
                continue
            
            # تحليل النية
            intent = analyze_intent(clean_user_input)
            print(f"🔍 نوع الإدخال: {intent}")
            
            # توليد الإجابة
            print("⏳ جاري التفكير...")
            
            # تحديد عدد الكلمات حسب النية
            if intent in ["تعريف", "طريقة", "سبب"]:
                next_words = 12
                temperature_msg = "إجابة مفصلة"
            elif intent == "سؤال":
                next_words = 10
                temperature_msg = "إجابة مباشرة"
            else:
                next_words = 8
                temperature_msg = "تكملة إبداعية"
            
            response = generate_text_advanced(
                clean_user_input,
                next_words,
                model,
                tokenizer,
                MAX_SEQUENCE_LEN
            )
            
            print(f"\n🤖 {temperature_msg}:")
            print(f"📝 {response}")
            
            # إحصائيات بسيطة
            response_words = len(response.split())
            input_words = len(clean_user_input.split())
            print(f"📊 الإدخال: {input_words} كلمة | الإخراج: {response_words} كلمة")
            
            conversation_count += 1
            
            # اقتراح تحسينات كل 5 محادثات
            if conversation_count % 5 == 0:
                print(f"\n💡 نصيحة: لقد أجريت {conversation_count} محادثات!")
                print("   جرب أسئلة مختلفة للحصول على إجابات متنوعة")
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. إلى اللقاء!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            print("💡 جرب إدخال نص أبسط")
            continue

if __name__ == "__main__":
    main()
