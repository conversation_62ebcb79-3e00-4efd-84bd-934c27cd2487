# -*- coding: utf-8 -*-
"""
تشغيل النموذج المتقدم المبسط للتفاعل المباشر
"""

import os
import pickle
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import load_model
import re

# إعدادات النموذج
MODEL_NAME = "ml-T1-Simple-Advanced"
MODEL_FILE = f"{MODEL_NAME}.h5"
TOKENIZER_FILE = f"{MODEL_NAME}_tokenizer.pkl"
MAX_SEQUENCE_LEN = 16

def clean_input(text: str) -> str:
    """تنظيف مدخل المستخدم"""
    text = re.sub(r'[^\u0600-\u06FF\s]', '', text)
    return text.strip()

def generate_text_advanced(seed_text: str, next_words: int, model, tokenizer, max_sequence_len: int):
    """توليد نص متقدم مع تحسينات للجودة"""
    output_text = seed_text
    used_words = set(seed_text.split())  # لتجنب التكرار المفرط

    for i in range(next_words):
        token_list = tokenizer.texts_to_sequences([output_text])[0]

        if len(token_list) > max_sequence_len - 1:
            token_list = token_list[-(max_sequence_len - 1):]

        token_list = pad_sequences([token_list], maxlen=max_sequence_len - 1, padding='pre')

        try:
            predicted_probs = model.predict(token_list, verbose=0)[0]

            # تحسين Top-K sampling
            top_k = min(10, len(predicted_probs))  # استخدام top-10 للتنوع
            top_k_indices = np.argpartition(predicted_probs, -top_k)[-top_k:]

            # ترتيب حسب الاحتمالية
            sorted_indices = top_k_indices[np.argsort(predicted_probs[top_k_indices])[::-1]]

            # اختيار كلمة مع تجنب التكرار المفرط
            selected_word = None
            for idx in sorted_indices:
                candidate_word = tokenizer.index_word.get(idx, "")
                if candidate_word and len(candidate_word) > 1:  # تجنب الكلمات القصيرة جداً
                    # تقليل احتمالية الكلمات المستخدمة مؤخراً
                    if candidate_word not in used_words or i > 3:
                        selected_word = candidate_word
                        break

            if not selected_word:
                # إذا لم نجد كلمة مناسبة، استخدم الأفضل
                predicted_index = sorted_indices[0]
                selected_word = tokenizer.index_word.get(predicted_index, "")

            if not selected_word or len(selected_word) < 2:
                break

            output_text += " " + selected_word
            used_words.add(selected_word)

            # تنظيف قائمة الكلمات المستخدمة كل فترة
            if len(used_words) > 15:
                used_words = set(list(used_words)[-10:])

        except Exception as e:
            break

    return output_text

def analyze_intent(text: str) -> str:
    """تحليل متقدم لنية المستخدم"""
    text_lower = text.lower()

    # التحيات
    if any(word in text_lower for word in ["السلام عليكم", "مرحبا", "أهلا", "هلا", "صباح", "مساء"]):
        return "تحية"
    elif any(word in text_lower for word in ["كيف حالك", "شلونك", "كيفك", "وش أخبارك"]):
        return "سؤال عن الحال"

    # أسئلة الهوية
    elif any(word in text_lower for word in ["من أنت", "ما اسمك", "مين انت"]):
        return "سؤال هوية"

    # أسئلة تعريفية
    elif any(word in text_lower for word in ["ما هو", "ما هي", "تعريف", "معنى"]):
        return "تعريف"
    elif any(word in text_lower for word in ["كيف", "طريقة", "كيفية"]):
        return "طريقة"
    elif any(word in text_lower for word in ["لماذا", "ليش", "سبب", "علة"]):
        return "سبب"
    elif any(word in text_lower for word in ["متى", "وقت", "تاريخ"]):
        return "وقت"
    elif any(word in text_lower for word in ["أين", "وين", "مكان", "موقع"]):
        return "مكان"
    elif any(word in text_lower for word in ["هل", "أم"]) or "؟" in text:
        return "سؤال"

    # شكر واعتذار
    elif any(word in text_lower for word in ["شكرا", "شكراً", "تسلم", "جزاك الله"]):
        return "شكر"
    elif any(word in text_lower for word in ["آسف", "معذرة", "أعتذر", "سامحني"]):
        return "اعتذار"

    # وداع
    elif any(word in text_lower for word in ["مع السلامة", "إلى اللقاء", "باي", "وداعا"]):
        return "وداع"

    else:
        return "عام"

def get_smart_response(user_input: str, intent: str, model, tokenizer, max_sequence_len: int) -> str:
    """توليد إجابة ذكية حسب النية"""

    # ردود جاهزة للتحيات والأسئلة الشائعة
    if intent == "تحية":
        responses = [
            "وعليكم السلام ورحمة الله وبركاته",
            "أهلاً وسهلاً بك",
            "مرحباً بك كيف يمكنني مساعدتك",
            "صباح النور",
            "مساء النور"
        ]
        return np.random.choice(responses)

    elif intent == "سؤال عن الحال":
        responses = [
            "الحمد لله بخير وأنت كيف حالك",
            "تمام الحمد لله وأنت شلونك",
            "منيح الحمد لله كيف صحتك",
            "كله زين الحمد لله وأنت كيفك"
        ]
        return np.random.choice(responses)

    elif intent == "سؤال هوية":
        responses = [
            "أنا أمؤلي نموذج ذكاء اصطناعي عربي أساعدك في الإجابة على أسئلتك",
            "اسمي أمؤلي وأنا مساعد ذكي باللغة العربية",
            "أنا نموذج ذكاء اصطناعي مصمم لمساعدتك وتقديم المعلومات"
        ]
        return np.random.choice(responses)

    elif intent == "شكر":
        responses = [
            "العفو لا شكر على واجب",
            "أهلاً وسهلاً",
            "تسلم الله يعطيك العافية",
            "حياك الله"
        ]
        return np.random.choice(responses)

    elif intent == "اعتذار":
        responses = [
            "لا بأس عليك مافي مشكلة",
            "عادي الجميع يخطئ",
            "لا تعتذر أنت محق",
            "طبعاً مسامح"
        ]
        return np.random.choice(responses)

    elif intent == "وداع":
        responses = [
            "مع السلامة في أمان الله",
            "إلى اللقاء",
            "الله معك",
            "تصبح على خير"
        ]
        return np.random.choice(responses)

    # للأسئلة الأخرى استخدم النموذج
    else:
        # إضافة كلمات مساعدة حسب النية
        if intent == "تعريف":
            enhanced_input = f"{user_input} هو"
        elif intent == "طريقة":
            enhanced_input = f"{user_input} يتم من خلال"
        elif intent == "سبب":
            enhanced_input = f"{user_input} لأن"
        else:
            enhanced_input = user_input

        return generate_text_advanced(
            enhanced_input,
            next_words=12,
            model=model,
            tokenizer=tokenizer,
            max_sequence_len=max_sequence_len
        )

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً بك في أمؤلي-T1 المتقدم المبسط!")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    if not os.path.exists(MODEL_FILE):
        print(f"❌ ملف النموذج غير موجود: {MODEL_FILE}")
        print("يرجى تشغيل t1_simple_advanced.py أولاً")
        return
    
    if not os.path.exists(TOKENIZER_FILE):
        print(f"❌ ملف Tokenizer غير موجود: {TOKENIZER_FILE}")
        print("يرجى تشغيل t1_simple_advanced.py أولاً")
        return
    
    # تحميل النموذج
    print("📥 تحميل النموذج...")
    try:
        model = load_model(MODEL_FILE)
        print(f"✅ تم تحميل النموذج: {MODEL_FILE}")
        
        with open(TOKENIZER_FILE, "rb") as f:
            tokenizer = pickle.load(f)
        print(f"✅ تم تحميل Tokenizer: {TOKENIZER_FILE}")
        
        print(f"🧠 النموذج جاهز مع {model.count_params():,} معامل")
        print(f"📚 حجم القاموس: {len(tokenizer.word_index):,} كلمة")
        
    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return
    
    # حلقة التفاعل
    print("\n🎯 النموذج جاهز للتفاعل!")
    print("💡 يمكنك:")
    print("   - طرح أسئلة: 'ما هو الذكاء الاصطناعي؟'")
    print("   - كتابة جملة للتكملة: 'في المستقبل ستكون التقنية'")
    print("   - كتابة 'خروج' لإنهاء البرنامج")
    print("-" * 50)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input(f"\n[{conversation_count + 1}] 🔤 أدخل نصك: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ["خروج", "exit", "quit"]:
                print("👋 شكراً لاستخدام أمؤلي-T1!")
                break
            
            # تنظيف الإدخال
            clean_user_input = clean_input(user_input)
            if not clean_user_input:
                print("❌ يرجى إدخال نص عربي صالح")
                continue
            
            # تحليل النية
            intent = analyze_intent(clean_user_input)
            print(f"🔍 نوع الإدخال: {intent}")

            # توليد الإجابة الذكية
            print("⏳ جاري التفكير...")

            response = get_smart_response(
                clean_user_input,
                intent,
                model,
                tokenizer,
                MAX_SEQUENCE_LEN
            )

            # تحديد نوع الرد
            if intent in ["تحية", "سؤال عن الحال", "سؤال هوية", "شكر", "اعتذار", "وداع"]:
                response_type = "رد تلقائي ذكي"
            elif intent in ["تعريف", "طريقة", "سبب"]:
                response_type = "إجابة مفصلة"
            else:
                response_type = "إجابة ذكية"

            print(f"\n🤖 {response_type}:")
            print(f"📝 {response}")
            
            # إحصائيات بسيطة
            response_words = len(response.split())
            input_words = len(clean_user_input.split())
            print(f"📊 الإدخال: {input_words} كلمة | الإخراج: {response_words} كلمة")
            
            conversation_count += 1
            
            # اقتراح تحسينات كل 5 محادثات
            if conversation_count % 5 == 0:
                print(f"\n💡 نصيحة: لقد أجريت {conversation_count} محادثات!")
                print("   جرب أسئلة مختلفة للحصول على إجابات متنوعة")
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج. إلى اللقاء!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            print("💡 جرب إدخال نص أبسط")
            continue

if __name__ == "__main__":
    main()
